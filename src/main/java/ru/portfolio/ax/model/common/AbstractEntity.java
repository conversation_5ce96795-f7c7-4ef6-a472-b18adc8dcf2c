package ru.portfolio.ax.model.common;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.rest.dto.ReachableDTO;
import ru.portfolio.ax.service.CrudService;

import javax.persistence.*;
import java.util.HashMap;
import java.util.Map;

import static io.swagger.annotations.ApiModelProperty.AccessMode.READ_ONLY;

@Data
@MappedSuperclass
@FieldNameConstants
public abstract class AbstractEntity<V> implements ReachableDTO {

    @Id
    @Column(updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
//    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @ApiModelProperty(position = 1, accessMode = READ_ONLY)
    private V id;

    @Override
    @SuppressWarnings("unchecked")
    public AbstractEntity reach(CrudService crudService) {//todo
        return this;
    }

    @Transient
    @JsonIgnore
    protected Map<String, Object> map = new HashMap<>();

    @JsonAnySetter
    public void setMap(String propertyKey, Object value) {
        this.map.put(propertyKey, value);
    }
}
