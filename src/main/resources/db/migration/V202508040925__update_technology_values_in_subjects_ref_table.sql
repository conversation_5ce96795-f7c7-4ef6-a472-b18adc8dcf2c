INSERT INTO portfolio.subjects_ref (value, code, adaptability, is_archive)
VALUES
    ('ОБ<PERSON>', 90, false, true),
    ('Безопасность', 91, false, true),
    ('Предпринимательство', 92, false, true),
    ('Планирование', 93, false, true),
    ('МХК', 94, false, true),
    ('Робототехника', 95, false, true),
    ('Информационная безопасность', 96, false, true),
    ('Изобразительное искусство', 97, false, true),
    ('Основы финансовой грамотности и предпринимательства', 98, false, true),
    ('История искусств', 99, false, true),
    ('Физкультура', 100, false, true),
    ('Программирование', 101, false, true),
    ('Основы безопасности и защиты Родины', 102, false, false),
    ('Генетика', 103, false, false),
    ('Вероятность и статистика', 104, false, false),
    ('Предпрофессиональная олимпиада: "Исследовательский сектор"', 105, false, false),
    ('Предпрофессиональная олимпиада: "Продуктовый сектор"', 106, false, false),
    ('Предпрофессиональная олимпиада: "Технологический сектор"', 107, false, false),
    ('Предпрофессиональная олимпиада: профиль "Арктика"', 108, false, false),
    ('Предпрофессиональная олимпиада: аэрокосмический профиль', 109, false, false),
    ('Предпрофессиональная олимпиада: инженерно-конструкторский профиль', 110, false, false),
    ('Предпрофессиональная олимпиада: информационно-технологический профиль', 111, false, false),
    ('Предпрофессиональная олимпиада: научно-исследовательский профиль', 112, false, false),
    ('Предпрофессиональная олимпиада: технологический профиль', 113, false, false),
    ('Предпрофессиональная олимпиада: химико-биотехнологический профиль', 114, false, false),
    ('Предпрофессиональная олимпиада: профиль "Электронные системы"', 115, false, false),
    ('Астрономия', 116, false, true),
    ('География и геология', 117, false, true),
    ('История физической культуры', 118, false, true),
    ('Основы электроники и электротехники', 119, false, true),
    ('Декоративно-прикладное искусство', 120, false, true),
    ('Музыкальная литература', 121, false, true),
    ('История театра и кино', 122, false, true),
    ('Архитектура и дизайн', 123, false, true),
    ('Военная история', 124, false, true),
    ('Труд (технология) информационная безопасность', 125, false, false)
ON CONFLICT (code) DO UPDATE SET value = excluded.value, adaptability = excluded.adaptability, is_archive = excluded.is_archive;

UPDATE portfolio.event SET subject_code = '125' WHERE subject_code ~ '126|127|128|129|130|131|132|133|134|135|136|137';
UPDATE portfolio.reward SET subject_code = '125' WHERE subject_code ~ '126|127|128|129|130|131|132|133|134|135|136|137';
UPDATE portfolio.project SET subject_code = '125' WHERE subject_code ~ '126|127|128|129|130|131|132|133|134|135|136|137';

DELETE FROM portfolio.subjects_ref WHERE code > 125;
