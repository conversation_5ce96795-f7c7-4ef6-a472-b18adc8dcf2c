CREATE TABLE portfolio.address
(
    code	varchar(300),
    value	varchar(300)
);

INSERT INTO portfolio.address (code,value) VALUES ('1', 'м. Красные ворота, Малый Харитоньевский переулок, 9/13с9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('2', 'м. Беговая, 3-й Хорошёвский проезд, 5с7, цех 1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('3', 'м. Тверская, улица Малая Дмитровка, 5/9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('4', 'м. Отрадное, улица Декабристов, 27, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('5', 'м. <PERSON>ю<PERSON>лино, Белореченская улица, 3, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('6', 'м. Люблино, Краснодарская улица, 45/11, школа №2121, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('7', 'м. Ольховая, пос. Коммунарка, посёлок Коммунарка, 11а, 3 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('8', 'м. Медведково, Лётная, 17, Мытищи');
INSERT INTO portfolio.address (code,value) VALUES ('9', 'м. Кунцевская, Можайское шоссе, 38к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('10', 'м. Беляево, Профсоюзная улица, 112, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('11', '"м. Хорошёво, проспект Маршала Жукова, 4 ст2, Пляжный центр ""Лето"", Москва"');
INSERT INTO portfolio.address (code,value) VALUES ('12', 'м. Селигерская, Дубнинская улица, 59, ГБОУ Школа №183, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('13', 'улица Фёдора Полетаева, 2к8, ГБОУ Школа на Юго-Востоке им. Маршала В.И. Чуйкова, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('14', 'м. Новокосино, Реутов, Советская улица, 6А, МБОУ СОШ №3 г. Реутов, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('15', 'Ходынский бульвар, 7, ГБОУ Школа №1409, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('16', '"село Ромашково, Никольская улица, 16, частная школа ""Лидеры"", Москва"');
INSERT INTO portfolio.address (code,value) VALUES ('17', 'м. Бабушкинская, Ярославское шоссе, 147, ГБОУ Школа №760 им. А.П. Маресьева, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('18', '"м. Багратионовская, Кастанаевская улица, 10, ГБОУ Школа ""Интеграл"", Москва"');
INSERT INTO portfolio.address (code,value) VALUES ('19', 'Красногорск, микрорайон Опалиха, улица Дежнёва, 14, МБДОУ Детский Сад №40, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('20', 'м. Коптево, Кронштадтский бульвар, 43А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('21', 'м. Беломорская, Валдайский проезд, 8, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('22', 'м. Братиславская, Люблинская улица, 88с1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('23', 'м. Марьина Роща, Шереметьевская улица, 29, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('24', 'м. Юго-Западная, проспект Вернадского, 78, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('25', 'м. Улица Горчакова, улица Горчакова, 5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('26', 'м. Жулебино, микрорайон Городок Б, улица 3-е Почтовое Отделение, 65к1, Люберцы');
INSERT INTO portfolio.address (code,value) VALUES ('27', 'м. Новые Черёмушки, улица Каховка, 37к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('28', 'м. Университет, улица Крупской, 1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('29', 'м. Тропарёво, проспект Вернадского, 94к4, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('30', 'м. Новопеределкино, 6-я улица Новые Сады, 2к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('31', 'м. Академическая, улица Гримау, 9к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('32', 'м. Братиславская, Марьинский бульвар, 8к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('33', 'м. Боровское шоссе, 5-я Чоботовская аллея, 9А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('34', 'м. Тропарёво, улица Островитянова, 6, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('35', 'городской округ село Николо-Урюпино, улица Гагарина, 23, Красногорск');
INSERT INTO portfolio.address (code,value) VALUES ('36', 'м. Улица Горчакова, Москва, Новостроевская улица, 6А, Щербинка');
INSERT INTO portfolio.address (code,value) VALUES ('37', 'м. Улица Горчакова, Москва, Бутовский тупик, 1к2, Щербинка');
INSERT INTO portfolio.address (code,value) VALUES ('38', 'м. Авиамоторная, Душинская улица, 7с1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('39', 'м. Дмитровская, Новодмитровская улица, 5А строение 2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('40', 'м. Алтуфьево, Шенкурский проезд, 8Г, школа №953, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('41', 'м. Белокаменная, 1-я Мясниковская улица, 18, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('42', 'м. Бабушкинская, Печорская улица, 6к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('43', 'м. Ботанический сад (Московское центральное кольцо), улица Седова, 12А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('44', 'м. Селигерская, Коровинское шоссе, 23 к2, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('45', 'м. Текстильщики, Волгоградский проспект, 42, фитнес-клуб Aquastar, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('46', 'м. Зорге, 3-я Хорошёвская улица, 21А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('47', 'м. Бабушкинская, проезд Дежнева, 1, 204 офис; 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('48', 'м. Савёловская, Правды, 24 ст2, 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('49', 'м. Сходненская, проезд Донелайтиса, 14, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('50', 'м. Речной вокзал, Петрозаводская улица, 15к5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('51', 'м. Нагатинская, улица Нагатинская, 16, 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('52', 'Авиационная улица, 3, Лобня');
INSERT INTO portfolio.address (code,value) VALUES ('53', 'м. Новогиреево, Мартеновская, 41 ст2, 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('54', 'м. Динамо, 1-й Боткинский проезд, 7с1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('55', 'м. Медведково, Тайнинская улица, 20, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('56', 'м. Бибирево, Высоковольтный проезд, 1к6, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('57', 'Соколово-Мещерская улица, 29, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('58', 'м. Отрадное, Юрловский проезд, 13, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('59', 'м. Лесопарковая, МКАД 36 Километр, 3 ст1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('60', 'м. Окружная, 3-й Нижнелихоборский проезд, 1с16, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('61', 'м. Бульвар Дмитрия Донского, улица Грина, 18Б, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('62', 'м. Бутырская, улица Добролюбова, 2с3, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('63', 'м. Новопеределкино, Шолохова, 30 к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('64', 'м. Улица Старокачаловская, Куликовская улица, 9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('65', 'м. Новокосино, Советская улица, 27, в Школе номер 7, Реутов');
INSERT INTO portfolio.address (code,value) VALUES ('66', 'м. Улица Скобелевская, рабочий посёлок Бутово, жилой комплекс Бутово Парк, 22, Фитнес СССР, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('67', 'м. Южная, Чертановская улица, 7к3, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('68', 'м. Мичуринский проспект, Лобачевского, 114 ст1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('69', 'м. Краснопресненская, Дружинниковская, 18, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('70', 'м. Севастопольская, Азовская улица, 31, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('71', 'м. Калужская, Научный проезд, 17, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('72', 'м. Дубровка, 1-я улица Машиностроения, 10, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('73', 'м. Свиблово, проезд Русанова, 2 ст1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('74', 'м. Селигерская, Бескудниковский бульвар, 21к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('75', 'м. Мякинино, Красногорск (Красногорский район), Зверева, 2, 1 этаж; вход со двора, Красногорск');
INSERT INTO portfolio.address (code,value) VALUES ('76', 'м. Фрунзенская, Комсомольский проспект, 32к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('77', 'Ленинский район, Завидная улица, 10, Видное');
INSERT INTO portfolio.address (code,value) VALUES ('78', 'м. Планерная, городской округ Красногорск, деревня Новотушинская улица, 9, МБОУ Школа «Мозаика», Путилково');
INSERT INTO portfolio.address (code,value) VALUES ('79', 'м. Зябликово, Елецкая улица, 17к3, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('80', 'м. Новогиреево, Федеративный проспект, 27, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('81', 'м. Юго-Западная, проспект Вернадского, 101к6, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('82', 'м. Парк культуры, улица Плющиха, 57с1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('83', 'м. Первомайская, Первомайская улица, 59, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('84', 'м. Преображенская площадь, улица Стромынка, 20к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('85', 'м. Царицыно, Каспийская улица, 24к4, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('86', 'г. Москва, ул. Рабочая, д. 53');
INSERT INTO portfolio.address (code,value) VALUES ('87', 'м. Октябрьское поле, улица Расплетина, 1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('88', 'м. Беломорская, Беломорская улица, 36, 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('89', 'МКАД, 97-ой км., внешняя сторона');
INSERT INTO portfolio.address (code,value) VALUES ('90', 'м. Ясенево, Литовский бульвар, 7, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('91', 'м. Новогиреево, Саянская, 6Б, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('92', 'м. Нагорная, Севастопольский проспект, 43А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('93', 'м. Строгино, Строгинский бульвар, 14к5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('94', '');
INSERT INTO portfolio.address (code,value) VALUES ('95', 'м. Фили, Большая Филёвская улица, 9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('96', 'м. Зябликово, Ясеневая улица, 26, 4 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('97', 'м. Технопарк, проспект Андропова, 8 ст2, 7 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('98', 'м. Арбатская (Филевская линия), Никитский бульвар, 15/16, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('99', 'м. Войковская, 2-й Новоподмосковный переулок, 3, клуб Развитие, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('100', 'м. Крестьянская застава, Таганская улица, 40с1А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('101', 'м. Шоссе Энтузиастов (Московское центральное кольцо), проспект Будённого, 26 к2, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('102', 'пос. Горки-2, посёлок Горки 2, ст23, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('103', 'м. Октябрьское поле, улица Маршала Конева, 11, ГБОУ Курчатовская школа, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('104', 'м. Коломенская, Кленовый бульвар, 23, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('105', 'м. Пражская, Варшавское шоссе, 129к2, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('106', ' г. Москва, ул. Академика Волгина, д. 33А');
INSERT INTO portfolio.address (code,value) VALUES ('107', 'м. Крылатское, Осенняя улица, 23, 10 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('108', 'м. Багратионовская, Большая Филёвская улица, 18, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('109', 'м. Сокольники, Леснорядский переулок, 18с6, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('110', 'м. Перово, 1-я Владимирская улица, 10Б, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('111', 'м. Алтуфьево, Дмитровское шоссе, 157с9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('112', 'м. Новогиреево, улица Сталеваров, 18к2, ДЦ Сфера, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('113', 'м. Перово, Зелёный проспект, 10б, Аквапарк Карибия, 4 этаж, зал №2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('114', 'м. Люблино, Белореченская, 3, 3 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('115', 'Видное, Жуковский проезд, 10/1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('116', 'м. Перово, Кусковская улица, 20а, 7 этаж, фитнес-центр, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('117', 'м. Новогиреево, улица Сталеваров, 14к1, Единение, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('118', 'м. Авиамоторная, шоссе Энтузиастов, 11 ст27, Б39 офис; 3 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('119', 'м. Щукинская, Авиационная улица, 57, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('120', 'м. Раменки, Мосфильмовская улица, 53, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('121', 'м. Юго-Восточная, Ферганская улица, 8, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('122', 'м. Аннино, Варшавское шоссе, 154А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('123', 'м. Кутузовская (Московское центральное кольцо), Кутузовский проспект, 36с3, подъезд 8 каб.108, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('124', 'м. Молодёжная, улица Боженко, 9, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('125', 'г. Москва, ул. Отрадная, д.16');
INSERT INTO portfolio.address (code,value) VALUES ('126', 'г. Москва, ул. Лесная, д.6');
INSERT INTO portfolio.address (code,value) VALUES ('127', 'м. Ломоносовский проспект, Мосфильмовская улица, 41к2, комплекс Юбилейный, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('128', 'м. Новогиреево, Напольный проезд, 7, Колледж железнодорожного и городского транспорта, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('129', 'м. Профсоюзная, улица Новочерёмушкинская, 34 к2, клуб СФЕРА, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('130', 'м. Аэропорт, улица 8 Марта, 17, Инженерно-техническая школа им. дважды Героя Советского Союза П.Р. Поповича, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('131', 'м. Измайлово, улица Ибрагимова, 32, ФОК МОЦВС, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('132', 'м. Некрасовка, Покровская улица, 35, школа №2053, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('133', 'м. Пятницкое шоссе, городской округ село Ангелово, жилой комплекс Ангелово-Резиденц, Красногорск');
INSERT INTO portfolio.address (code,value) VALUES ('134', 'м. Багратионовская, Новозаводская, 27а ст1, СК Фили, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('135', '"м. Багратионовская, Большая Филёвская улица, 15, образовательный центр ""Протон"", Москва"');
INSERT INTO portfolio.address (code,value) VALUES ('136', 'м. Севастопольская, Балаклавский проспект, 33с2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('137', 'м. Юго-Западная, проспект Вернадского, 78 ст5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('138', 'Москва, к621А, школа №854, Зеленоград');
INSERT INTO portfolio.address (code,value) VALUES ('139', 'м. Юго-Западная, проспект Вернадского, 78с5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('140', 'м. Озёрная, Никулинская улица, 5, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('141', 'м. Владыкино, Алтуфьевское шоссе, 12Б, СОК Отрадное, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('142', 'м. Планерная, улица Вилиса Лациса, 8к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('143', 'м. Минская, улица Улофа Пальме, 5с2, СРК Премьер-Спорт, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('144', 'м. Перово, Новогиреевская улица, 22А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('145', 'м. ВДНХ, улица Космонавтов, 11, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('146', 'м. Окружная, 3-й Нижнелихоборский проезд, 1с6, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('147', 'м. Коломенская, Метро Коломенская, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('148', 'м. Медведково, Борисовка, 12а, 1 этаж, Мытищи');
INSERT INTO portfolio.address (code,value) VALUES ('149', 'м. Волоколамская, Пятницкое шоссе, 15 к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('150', 'м. Дубровка, Кожуховская 6-я, 11 к2, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('151', 'м. Улица Академика Янгеля, Кировоградский проезд, 3к1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('152', 'Красная улица, 31, 3 этаж, Электросталь');
INSERT INTO portfolio.address (code,value) VALUES ('153', 'м. Измайловская, Парковая 4-я, 9/21, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('154', 'м. Петровский парк, улица Верхняя Масловка, 25, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('155', 'м. Перово, Перовская улица, 66к3, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('156', 'м. ЦСКА, Ленинградский проспект, 39с1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('157', 'г. Москва, Волоколамское ш., д. 86');
INSERT INTO portfolio.address (code,value) VALUES ('158', 'м. Ломоносовский проспект, Ломоносовский проспект, 29к1, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('159', 'м. Измайловская, Первомайская, 42 к1, 2 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('160', 'м. Митино, Красногорск, Дачная улица, 11А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('161', 'м. Южная, Кировоградская улица, 10А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('162', 'м. Калужская, Обручева, 23 к3, клуб Ермак, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('163', 'м. Крылатское, улица Крылатские Холмы, 15к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('164', 'м. Октябрьская, улица Крымский Вал, 8к2, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('165', 'м. Бабушкинская, Ярославское шоссе, 19 ст1, 4 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('166', 'м. Новогиреево, Свободный проспект, 19, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('167', 'м. Измайловская, 4-я Парковая улица, 9/21, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('168', 'м. Медведково, Веры Волошиной, 48, Мытищи');
INSERT INTO portfolio.address (code,value) VALUES ('169', 'м. Багратионовская, Новозаводская, 27а ст1, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('170', 'м. Юго-Западная, Ленинский проспект, 113/1, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('171', 'Московская обл., 5 км от МКАД, Ново-Рижское шоссе, усадьба Архангельское, коттеджный поселок «Захарково», Москва');
INSERT INTO portfolio.address (code,value) VALUES ('172', 'м. Шелепиха, улица Литвина-Седого, 3А, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('173', 'м. Митино, д. МКАД 71 км, 16а, 1 этаж, Путилково');
INSERT INTO portfolio.address (code,value) VALUES ('174', 'м. Верхние Котлы, Варшавское шоссе, 18 к2, цокольный этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('175', 'м. Новокузнецкая, Космодамианская Набережная, 4/22 к Б, 1 этаж, Москва');
INSERT INTO portfolio.address (code,value) VALUES ('176', 'м. Бунинская аллея, улица Адмирала Руднева, 20, Москва');