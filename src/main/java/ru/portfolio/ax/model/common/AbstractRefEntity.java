package ru.portfolio.ax.model.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import ru.portfolio.ax.repository.common.GenericProjection;
import ru.portfolio.ax.rest.dto.AbstractDTO;

import javax.persistence.*;

import static io.swagger.annotations.ApiModelProperty.AccessMode.READ_ONLY;

@Data
@MappedSuperclass
@FieldNameConstants
public abstract class AbstractRefEntity<V> implements AbstractDTO, GenericProjection<V> {

    @Id
    @Column(updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @JsonProperty(access = JsonProperty.Access.READ_WRITE)
    @ApiModelProperty(position = 1, accessMode = READ_ONLY)
    private V code;

    @Override
    public V getId() {
        return code;
    }

    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class Fields {
    }
}
