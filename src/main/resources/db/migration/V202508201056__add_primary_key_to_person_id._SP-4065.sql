-- SP-4065: Добавление первичного ключа к таблице person_id

-- Проверяем и исправляем записи с NULL id в person_id
UPDATE portfolio.person_id
SET id = nextval('portfolio.hibernate_sequence')
WHERE id IS NULL;

-- Добавляем первичный ключ к person_id (если его нет)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_schema = 'portfolio'
        AND table_name = 'person_id'
        AND constraint_type = 'PRIMARY KEY'
    ) THEN
        ALTER TABLE portfolio.person_id
        ADD CONSTRAINT person_id_pkey PRIMARY KEY (id);
    END IF;
END $$;

-- Проверяем и исправляем записи с NULL id в person_id_update
UPDATE portfolio.person_id_update
SET id = nextval('portfolio.hibernate_sequence')
WHERE id IS NULL;

-- Добавляем первичный ключ к person_id_update (если его нет)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE table_schema = 'portfolio'
        AND table_name = 'person_id_update'
        AND constraint_type = 'PRIMARY KEY'
    ) THEN
        ALTER TABLE portfolio.person_id_update
        ADD CONSTRAINT person_id_update_pkey PRIMARY KEY (id);
    END IF;
END $$;

-- Убеждаемся, что последовательность существует в правильной схеме
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.sequences
        WHERE sequence_schema = 'portfolio'
        AND sequence_name = 'hibernate_sequence'
    ) THEN
        CREATE SEQUENCE portfolio.hibernate_sequence START WITH 1 INCREMENT BY 1;
    END IF;
END $$;