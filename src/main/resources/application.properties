server.servlet.contextPath=/${SERVER_PATH:app}
server.max-http-header-size=40KB

server.port=${SERVER_PORT:8080}
spring.flyway.enabled=${ENABLE_FLYWAY:true}

springfox.documentation.auto-startup=${ENABLE_SWAGGER:false}

# Database configuration
spring.datasource.originUrl=${DATASOURCE_HOST:docker-srv09.axbit.ru}
spring.datasource.originPort=${DATASOURCE_PORT:5434}

hikari.logback.level = ${HIKARI_LOGBACK_LEVEL:debug}
spring.datasource.leakDetectionThreshold = ${LEAK_DETECTION_THRESHOLD:60000}
spring.datasource.idlePoolSize = ${IDLE_POOL_SIZE:10}
spring.datasource.idleTime = ${IDLE_TIMEOUT:60000}
#spring.datasource.hikari.connection-timeout = ${CONNECTION_HIKARI_TIMEOUT:60000}
#spring.datasource.connection-timeout = ${CONNECTION_TIMEOUT:60000}
#spring.datasource.max-lifetime=${MAX_LIFETIME:}
spring.datasource.url=jdbc:postgresql://${spring.datasource.originUrl}:${spring.datasource.originPort}/${DATASOURCE_DB:portfolio}
spring.datasource.username=${DATASOURCE_USERNAME:postgres}
spring.datasource.password=${DATASOURCE_PASSWORD:postgres}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.schema=${DATASOURCE_SCHEMA:portfolio}
spring.datasource.maximumPoolSize=${CONNECTION_POOL_SIZE:20}

spring.datasource.replica.hosts=${DATASOURCE_REPLICA_HOSTS:${spring.datasource.originUrl}}
spring.datasource.replica.ports=${DATASOURCE_REPLICA_PORTS:5433}
spring.datasource.replica.users=${DATASOURCE_REPLICA_USERS:${DATASOURCE_USERNAME:postgres}}
spring.datasource.replica.passwords=${DATASOURCE_REPLICA_PASSWORDS:${DATASOURCE_PASSWORD:postgres}}

spring.data.web.pageable.max-page-size=9999
spring.data.rest.max-page-size=9999

spring.jpa.properties.hibernate.default_schema=${DATASOURCE_SCHEMA:portfolio}
spring.jpa.properties.hibernate.dialect=org.hibernate.spatial.dialect.postgis.PostgisPG95Dialect
spring.jpa.database-platform=org.hibernate.spatial.dialect.postgis.PostgisPG95Dialect
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
spring.jpa.properties.hibernate.globally_quoted_identifiers=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false

logging.interceptor.enabled=${IN_OUT_LOGGING_INTERCEPTOR:true}
migration.regions=${MIGRATION_REGIONS:false}
spring.jpa.properties.hibernate.connection.handling_mode=${CONNECTION_HANDLING_MODE:DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION}
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
server.tomcat.max-http-post-size=50MB

http.max-per-route=${HTTP_MAX_PER_ROUTE:100}
http.max-total=${HTTP_MAX_TOTAL:200}

click-house.url=jdbc:clickhouse://${CLICK_HOUSE_HOST:clickhouse-rp-01-sh-01.axbit.ru:8123,clickhouse-rp-02-sh-01.axbit.ru:8123,clickhouse-rp-03-sh-02.axbit.ru:8123,clickhouse-rp-04-sh-02.axbit.ru}:${CLICK_HOUSE_PORT:8123}
click-house.connector-fail-on-error=${CLICK_HOUSE_CONNECTOR_FAIL_ON_ERROR:false}
click-house.connector-enable=${CLICK_HOUSE_CONNECTOR_ENABLE:false}
click-house.connector-host=${CLICK_HOUSE_CONNECTOR_HOST:http://click-house-connector.dev.k8s.axbit.ru:32561/app/}
click-house.login=${CLICK_HOUSE_LOGIN:admin}
click-house.password=${CLICK_HOUSE_PASSWORD:123}
click-house.schema=${CLICK_HOUSE_SCHEMA:portfolio_db}
click-house.cluster=${CLICK_HOUSE_CLUSTER:company_cluster}
click-house.table=${CLICK_HOUSE_TABLE:distributed_learner_portfolio}
click-house.proftech.table=${CLICK_HOUSE_PROFTECH_TABLE:distributed_proftech_portfolio}
click-house.lessons.table=${CLICK_HOUSE_LESSONS_TABLE:distributed_lessons}
click-house.distributed-diagnostic-notification.table=${CLICK_HOUSE_D_NOTIFICATION_TABLE:distributed_notifications}
click-house.diagnostic-notification.table=${CLICK_HOUSE_NOTIFICATION_TABLE:MATDiagnosticNotifications}
click-house.diagnostic-notification.optimize=${CLICK_HOUSE_NOTIFICATION_OPTIMIZE:true}

click-house.apiKey=${CLICK_HOUSE_API_KEY:}
click-house.connectionTimeout=${CLICK_HOUSE_CONNECTION_TIMEOUT:10000}
click-house.keepAliveTimeout=${CLICK_HOUSE_KEEP_ALIVE_TIMEOUT:30000}
click-house.timeToLiveTimeout=${CLICK_HOUSE_TIME_TO_LIVE_TIMEOUT:60000}
click-house.socketTimeout=${CLICK_HOUSE_SOCKET_TIMEOUT:30000}
click-house.dataTransferTimeout=${CLICK_HOUSE_DATA_TRANSFER_TIMEOUT:10000}
context.update-educations=${CONTEXT_UPDATE_EDUCATIONS:false}
context.actualize-persons=${CONTEXT_ACTUALIZE_PERSONS:false}

click-house.general-region-rating.table=${CLICK_HOUSE_GENERAL_REGION_RATING:distributed_diagnostic_general_region_rating}
click-house.general-school-rating.table=${CLICK_HOUSE_GENERAL_SCHOOL_RATING:distributed_diagnostic_general_school_rating}
click-house.internal-region-level.table=${CLICK_HOUSE_INTERNAL_REGION_LEVEL:distributed_diagnostic_internal_region_level}
click-house.internal-region-person.table=${CLICK_HOUSE_INTERNAL_REGION_PERSON:distributed_diagnostic_internal_region_person}
click-house.internal-region-rating.table=${CLICK_HOUSE_INTERNAL_REGION_RATING:distributed_diagnostic_internal_region_rating}
click-house.internal-school-level.table=${CLICK_HOUSE_INTERNAL_SCHOOL_LEVEL:distributed_diagnostic_internal_school_level}
click-house.internal-school-person.table=${CLICK_HOUSE_INTERNAL_SCHOOL_PERSON:distributed_diagnostic_internal_school_person}
click-house.internal-school-rating.table=${CLICK_HOUSE_INTERNAL_SCHOOL_RATING:distributed_diagnostic_internal_school_rating}

click-house.marks-average.table=${CLICK_HOUSE_MARKS_AVERAGE:distributed_marks_average}
click-house.marks-final.table=${CLICK_HOUSE_MARKS_FINAL:distributed_marks_final}

general-rating.best.enabled=${GENERAL_RATING_BEST_ENABLED:false}

portfolio-link=${PORTFOLIO_LINK:https://portfolio-dev.mos.ru/shared-link/}

mesh-contingent.host=${CONTINGENT_HOST:https://mes-api-test.mos.ru/}
mesh-contingent.app=${CONTINGENT_APP:a28a0e13-89ad-48ad-b05b-a674e3697c9a}
mesh-contingent.patient=${CONTINGENT_HOST_PATIENT:${mesh-contingent.host}/contingent/persons/}
mesh-contingent.classes=${CONTINGENT_HOST_CLASSES:${mesh-contingent.host}/contingent/classes/}
mesh-contingent.oldContingentCategoryId =${OLD_CONTINGENT_CATEGORY_ID:1}
mesh-contingent.newContingentCategoryId =${NEW_CONTINGENT_CATEGORY_ID:1}
mesh-contingent.nsi1CategoryId =${NEW_CONTINGENT_CATEGORY_ID:1}
mesh-contingent.nsi2CategoryId =${NEW_CONTINGENT_CATEGORY_ID:1}

nsi.host=${NSI_HOST:https://mes-api-test.mos.ru/nsi/v1/catalog/get}
nsi.app=${NSI_APP:portfolio}
nsi.key=${NSI_KKY:8570f457-340b-4af3-a0b1-5f16419c4b79}
nsi.orgId=${ORGANIZATION_REGISTRY_CODE:478}
nsi.staffId=${STAFF_REGISTRY_CODE:897}
nsi.cache.duration.minutes=${NSI_CACHE_DURATION_MINUTES:1440}
nsi.cache.maxItems=${NSI_CACHE_MAX_ITEMS:5000}

esz.host=${ESZ_HOST:https://mes-api-test.mos.ru/circles/circles}
esz.token=${ESZ_TKN:Basic VGVzdFVzZXI6VGVzdFBhc3M=}
esz.key=${ESZ_KKY:ca83f817-958c-49e8-a1a1-11d6d9d80851}

library.host=${LIBRARY_HOST:https://uchebnik.mos.ru/api/lib/search/v2/materials}
library.token=${LIBRARY_TOKEN:}
library.key=${LIBRARY_KEY:}

dop.url=${DOP_URL:https://booking.stage4.navi.inlearno.info/api/mes-integration/event/}
dop.token=${DOP_TOKEN:Bearer 312312312414124}
dop.tls=${DOP_TLS:true}

ej.api.url=${EJ_API_URL:https://school-dev.mos.ru/api/ej/}
ej.api.rankClassEndpoint=${EJ_API_RANK_CLASS_ENDPOINT:rating/v1/rank/class}
ej.api.rankSubjectsEndpoint=${EJ_API_RANK_SUBJECTS_ENDPOINT:rating/v1/rank/subjects}
ej.api.key=${EJ_API_KEY:00eccf4f-1cf1-4444-a6c8-3b45fb0816a5}

school.host=${SCHOOL_HOST:https://school.mos.ru/avatars/}

aupd.url=${AUPD_URL:https://school-dev.mos.ru/v1/}
#aupd.url=${AUPD_URL:https://mes-api.mos.ru/aupd/v1/}
aupd.subsystemId=${AUPD_SUBSYSTEMID:4}
aupd.issuer=${AUPD_TOKEN_ISSUER:https://school-dev.mos.ru/}
aupd.certName=${AUPD_CERT:key_dev.cer}
#aupd.issuer=${AUPD_TOKEN_ISSUER:https://school.mos.ru/}
aupd.key=${AUPD_KEY:b36da08b-7fb3-4487-9590-f13671125363}
#aupd.key=${AUPD_KEY:df1e841f-5c33-42ae-8083-af3cd7217f84}

ceds.host=${CEDS_HOST:https://doc-upload2-stage.mos.ru/adapters-ched/rest/api/document/}
ceds.systemCode=${CEDS_SYSTEM_CODE:ais_pf_uchash_dit}
ceds.password=${CEDS_PASSWORD:#Io0wBWhgV@3RpzbmniMx}
ceds.repository=${CEDS_REPOSITORY:DIT}

#proforientation.url = ${PROF_URL:https://school-dev.mos.ru/api/proforientation/crm/v1}
proforientation.url = ${PROF_URL:https://school-dev.mos.ru/api/proforientation/crm/v1}
proforientation.xapikey = ${PROFORIENTATION_APP:11ff30c6-35fe-4210-ba30-b14b35553858}

proforientation.authAction = ${PROF_AUTH_ACTION:User/Authentication}
proforientation.login = ${PROF_LOGIN:dit}
proforientation.password = ${PROF_PASSWORD:0OCmooId3Abv}

old.auth.enabled = ${OLD_AUTH_ENABLED:false}

roles.local.admin = ${ADMIN_LOCAL_ROLE:130}
roles.local.headTeacherId = ${HEAD_TEACHER_LOCAL_ROLE_ID:99}
roles.local.teacherOO =  ${TEACHER_O_O:136}
roles.local.employeeOO =  ${EMPLOYEE_OO:131}
roles.local.adminOO =  ${ADMIN_O_O:133}
roles.local.adminRO =  ${ADMIN_R_O:134}
roles.global.employeeId = ${EMPLOYEE_GLOBAL_ROLE_ID:3}
roles.global.parentId = ${AGENT_GLOBAL_ROLE_ID:2}
roles.global.childId = ${STUDENT_GLOBAL_ROLE_ID:1}
roles.global.adminId = ${ADMIN_GLOBAL_ROLE_ID:16}
roles.global.headTeacherId = ${HEAD_TEACHER_GLOBAL_ROLE_ID:8}
roles.global.schoolAdminId  = ${SCHOOL_ADMIN_GLOBAL_ROLE_ID:26}
roles.global.teacherId = ${TEACHER_GLOBAL_ROLE_ID:9}
roles.global.operatorId = ${OPERATOR_GLOBAL_ROLE_ID:19}
roles.global.studentId = ${STUDENT_SPO_GLOBAL_ROLE:32}

roles.ids.operator = ${GLOBAL_OPERATOR:32},${STUDY_OPERATOR:33},${SCIENCE_OPERATOR:34},${SPORT_OPERATOR:35},${CREATION_OPERATOR:36},${CULTURE_OPERATOR:37},${CIVIL_OPERATOR:38},${PROFESSION_OPERATOR:39}

roles.operator.adminOperator = ${roles.local.admin}
roles.operator.studentOperator = ${STUDENT_OPERATOR:30}
roles.operator.agentOperator = ${AGENT_OPERATOR:31}
roles.operator.globalOperator = ${GLOBAL_OPERATOR:32}
roles.operator.studyOperator = ${STUDY_OPERATOR:33}
roles.operator.scienceOperator = ${SCIENCE_OPERATOR:34}
roles.operator.sportOperator = ${SPORT_OPERATOR:35}
roles.operator.creationOperator = ${CREATION_OPERATOR:36}
roles.operator.cultureOperator = ${CULTURE_OPERATOR:37}
roles.operator.civilOperator = ${CIVIL_OPERATOR:38}
roles.operator.professionOperator = ${PROFESSION_OPERATOR:39}
roles.operator.teacherOO =  ${TEACHER_O_O:136}
roles.operator.employeeOO =  ${EMPLOYEE_OO:131}
roles.operator.adminOO =  ${ADMIN_O_O:133}
roles.operator.adminRO =  ${ADMIN_R_O:134}
#roles.operator.studentSpo = ${STUDENT_SPO:139}

roles.operator-cat.globalOperator = ${GLOBAL_OPERATOR_CAT:}
roles.operator-cat.studyOperator = ${STUDY_OPERATOR_CAT:1}
roles.operator-cat.scienceOperator = ${SCIENCE_OPERATOR_CAT:2}
roles.operator-cat.sportOperator = ${SPORT_OPERATOR_CAT:3}
roles.operator-cat.creationOperator = ${CREATION_OPERATOR_CAT:4}
roles.operator-cat.cultureOperator = ${CULTURE_OPERATOR_CAT:5}
roles.operator-cat.civilOperator = ${CIVIL_OPERATOR_CAT:6}
roles.operator-cat.professionOperator = ${PROFESSION_OPERATOR_CAT:68}
roles.operator-cat.adminOperator =  ${ADMIN_OPERATOR_CAT:0}
roles.operator-cat.adminRO =  ${ADMIN_R_O_OPERATOR_CAT:0}

roles.operator-ent.globalOperator = ${GLOBAL_OPERATOR_ENT:}
roles.operator-ent.studyOperator = ${STUDY_OPERATOR_ENT:Event,Reward}
roles.operator-ent.scienceOperator = ${SCIENCE_OPERATOR_ENT:Event,Reward,Project,Employment}
roles.operator-ent.sportOperator = ${SPORT_OPERATOR_ENT:Event,SportReward,Affilation,Employment}
roles.operator-ent.creationOperator = ${CREATION_OPERATOR_ENT:Event,Reward,Affilation,Employment}
roles.operator-ent.cultureOperator = ${CULTURE_OPERATOR_ENT:Event}
roles.operator-ent.civilOperator = ${CIVIL_OPERATOR_ENT:Event,Reward,Affilation,Employment}
roles.operator-ent.professionOperator = ${PROFESSION_OPERATOR_ENT:Event,Reward,GIAWorldskills}
roles.operator-ent.adminOperator =  ${ADMIN_OPERATOR_ENT:None}
roles.operator-ent.adminRO =  ${ADMIN_R_O_OPERATOR_ENT:None}

cron.updatePersonId = ${CRON_UPDATE_VIEW:0 0 0/2 ? * *}
cron.deleteOldShareLink = ${CRON_DELETE_OLD_SHARE_LINK:0 0 15 * * *}

#cron.readClickhouseDiagnostic = ${CRON_READ_CLICKHOUSE_DIAGNOSTIC:0 30/30 * ? * *}
#cron.sendNotification = ${CRON_SEND_NOTIFICATION:0 0/30 * ? * *}
cron.sendGratitudeTeacher = ${CRON_SEND_GRATITUDE_TEACHER:0 0 1/2 ? * *}
cron.readClickhouseDiagnostic = ${CRON_READ_CLICKHOUSE_DIAGNOSTIC:0 0/30 * ? * *}
cron.sendNotification = ${CRON_SEND_NOTIFICATION:0 30/30 * ? * *}
cron.checkFailedExcel = ${CRON_CHECK_FAIL_EXCEL:0 0 0 ? * *}

logging.level.root = info

fos.url=${FOS_URL:https://school.mos.ru/fos/api/fos/v1/url/}
fos.product=${FOS_PRODUCT:portfolio}

excel.fileMaxRowCount=${IMPORT_FILE_MAX_ROWS_COUNT:10000}
excel.partitionSize=${IMPORT_DATA_PARTITION_SIZE:250}

kafka.username=${KAFKA_USERNAME:portfolio}
kafka.password=${KAFKA_PASSWORD:portfolio}
kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVERS:212.11.151.26:9093,212.11.151.53:9093,212.11.151.52:9093}
kafka.topic=${KAFKA_TOPIC:messages.from.any.to.eks}
kafka.producer.enabled=${KAFKA_PRODUCER_ENABLED:false}
kafka.consumer.enabled=${KAFKA_CONSUMER_ENABLED:false}

kafka.retry-count=${KAFKA_RETRY_COUNT:20}

kafka.education.topic=${KAFKA_EDUCATION_TOPIC:education.fct.uchi-contest-result.1}
kafka.education.group-id=${KAFKA_EDUCATION_GROUP_ID:portfolio_group}
kafka.education.bootstrap-servers=${KAFKA_EDUCATION_BOOTSTRAP_SERVERS:212.11.151.26:9093,212.11.151.53:9093,212.11.151.52:9093}
kafka.education.auto-startup=${KAFKA_EDUCATION_AUTO_STARTUP:false}
kafka.education.max-poll-records=${KAFKA_MAX_POLL_RECORDS:500}
kafka.education.max-poll-interval-ms=${KAFKA_MAX_POLL_INTERVAL_MS:300000}
kafka.education.session-timeout-ms=${KAFKA_SESSION_TIMEOUT_MS:45000}

kafka.notifications.topic=${KAFKA_NOTIFICATION_TOPIC:eks.cmd.portfolio.0}
kafka.gratitude-teacher.topic=${GRATITUDE_TEACHER_TOPIC:portfolio.fct.gratitude-achievements.0}

kafka.notifications.push.enabled=${KAFKA_NOTIFICATION_PUSH_ENABLED:true}
kafka.notifications.email.enabled=${KAFKA_NOTIFICATION_EMAIL_ENABLED:false}

notifications.share.url=${NOTIFICATIONS_URL:https://portfolio.mos.ru/portfolio/student/study?student=}
notifications.clickhouse.limit=${NOTIFICATIONS_CLICKHOUSE_LIMIT:1000}
notifications.clickhouse.days-old-diagnostic=${NOTIFICATIONS_CLICKHOUSE_DAYS_OLD:1}

external.rest-timeout.low=${REST_TIMEOUT_TIME_LOW:5000}
external.rest-read-timeout.low=${REST_READ_TIMEOUT_TIME_LOW:5000}

external.rest-timeout=${REST_TIMEOUT_TIME:10000}
external.rest-read-timeout=${REST_READ_TIMEOUT_TIME:10000}

external.rest-timeout.high=${REST_TIMEOUT_TIME_HIGH:30000}
external.rest-read-timeout.high=${REST_READ_TIMEOUT_TIME_HIGH:30000}

clickhouse.logs.enabled=${CLICKHOUSE_LOGS_ENABLED:false}
clickhouse.use-material-views=${CLICKHOUSE_USE_MATERIAL_VIEWS:true}

s3.access.key.id=${S3_KEY_ID:4TsmJXejQXDNKSE4n8f6xh}
s3.access.key.secret=${S3_KEY_SECRET:dmqJgubkBxQi7NPARpkRYHNgQfXxhuVNCJtLwPZrJPpn}
s3.region.name=${S3_REGION_NAME:consent}
s3.bucket.name=${S3_BUCKET_NAME:s3b-mes-portfolio-test}
s3.address=${S3_ADDRESS:https://s3-dc.mos.ru}

attachments.ceds = ${ATTACHMENTS_CEDS:false}

diagnostic.school-rating.enabled=${SCHOOL_RATING_ENABLED:true}

management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=health,prometheus,beans,caches

# actuator basic authorization
spring.security.user.name=${SECURITY_USER:}
spring.security.user.password=${SECURITY_PASSWORD:}
spring.security.user.roles=ACTUATOR

javamelody.enabled=${????????? ????????? ????????? ? ?????:false}
javamelody.init-parameters.authorized-users=${MELODY_LOGIN:}:${MELODY_PASSWORD:}
javamelody.init-parameters.log=${MELODY_LOG:false}
#spring.aop.proxy-target-class=true

#?????????????? ?????????? ?????????? Kafka ??????
partition.management.enabled=true
partition.management.retention-months=12
